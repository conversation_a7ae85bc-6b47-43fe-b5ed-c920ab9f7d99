<!-- Professional Footer -->
<footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Main Footer Content -->
    <div class="py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        
        <!-- Company Info -->
        <div class="lg:col-span-2">
          <!-- Logo -->
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold">AI Marketing Hub</h3>
          </div>
          
          <!-- Mission Statement -->
          <p class="text-gray-300 mb-6 max-w-md">
            Revolutionize your marketing with AI-powered automation. Create smarter campaigns, 
            analyze deeper insights, and grow your business 10x faster than traditional methods.
          </p>
          
          <!-- Social Links -->
          <div class="flex space-x-4">
            <a href="#" aria-label="Twitter" class="text-gray-400 hover:text-white transition-colors duration-200 transform hover:scale-110">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </a>
            
            <a href="#" aria-label="LinkedIn" class="text-gray-400 hover:text-white transition-colors duration-200 transform hover:scale-110">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
            </a>
            
            <a href="#" aria-label="GitHub" class="text-gray-400 hover:text-white transition-colors duration-200 transform hover:scale-110">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
            </a>
            
            <a href="#" aria-label="YouTube" class="text-gray-400 hover:text-white transition-colors duration-200 transform hover:scale-110">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
            </a>
          </div>
          
          <!-- Newsletter Signup -->
          <div class="mt-8">
            <h4 class="text-lg font-semibold mb-3">Stay Updated</h4>
            <p class="text-gray-400 text-sm mb-4">Get the latest AI marketing insights and updates.</p>
            <form class="flex flex-col sm:flex-row gap-3">
              <input type="email" placeholder="Enter your email" 
                     class="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <button type="submit" 
                      class="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200">
                Subscribe
              </button>
            </form>
          </div>
        </div>
        
        <!-- Product Links -->
        <div>
          <h3 class="text-lg font-semibold mb-4">Product</h3>
          <ul class="space-y-3">
            <% unless user_signed_in? %>
              <li>
                <%= link_to  features_path, 
                    class: "text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group" do %>
                  <span>Features</span>
                  <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              </li>
              <li>
                <%= link_to  pricing_path, 
                    class: "text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group" do %>
                  <span>Pricing</span>
                  <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              </li>
            <% else %>
              <li>
                <%= link_to  dashboard_path, 
                    class: "text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group" do %>
                  <span>Dashboard</span>
                  <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              </li>
              <li>
                <%= link_to  campaigns_path, 
                    class: "text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group" do %>
                  <span>Campaigns</span>
                  <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                <% end %>
              </li>
            <% end %>
            
            <li>
              <a href="#" class="text-gray-400 cursor-not-allowed flex items-center space-x-2">
                <span>Integrations</span>
                <span class="text-xs bg-gray-800 text-gray-500 px-2 py-1 rounded-full">Soon</span>
              </a>
            </li>
            <li>
              <a href="#" class="text-gray-400 cursor-not-allowed flex items-center space-x-2">
                <span>API Access</span>
                <span class="text-xs bg-gray-800 text-gray-500 px-2 py-1 rounded-full">Soon</span>
              </a>
            </li>
            <li>
              <a href="#" class="text-gray-400 cursor-not-allowed flex items-center space-x-2">
                <span>Mobile App</span>
                <span class="text-xs bg-gray-800 text-gray-500 px-2 py-1 rounded-full">2024</span>
              </a>
            </li>
          </ul>
        </div>
        
        <!-- Support Links -->
        <div>
          <h3 class="text-lg font-semibold mb-4">Support</h3>
          <ul class="space-y-3">
            <li>
              <a href="#" class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group">
                <span>Help Center</span>
                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="#" class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group">
                <span>Documentation</span>
                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="#" class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group">
                <span>Contact Us</span>
                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
            <li>
              <a href="#" class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group">
                <span>System Status</span>
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </a>
            </li>
            <li>
              <%= link_to  about_path, 
                  class: "text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group" do %>
                <span>About Us</span>
                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              <% end %>
            </li>
            <li>
              <a href="#" class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center space-x-2 group">
                <span>Community</span>
                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- Bottom Footer -->
    <div class="border-t border-gray-800 py-6">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        
        <!-- Copyright -->
        <div class="text-gray-400 text-sm">
          &copy; <%= Date.current.year %> AI Marketing Hub. All rights reserved.
        </div>
        
        <!-- Trust Indicators -->
        <div class="flex items-center space-x-6 text-sm text-gray-400">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
            </svg>
            <span>SSL Secured</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>SOC 2 Compliant</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>99.9% Uptime</span>
          </div>
        </div>
        
        <!-- Legal Links -->
        <div class="flex items-center space-x-6 text-sm">
          <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">Privacy Policy</a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">Terms of Service</a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">Cookie Policy</a>
        </div>
      </div>
    </div>
  </div>
</footer>