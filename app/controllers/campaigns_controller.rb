# frozen_string_literal: true

class CampaignsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign, only: [:show, :edit, :update, :destroy, :activate, :pause, :complete]

  def index
    @campaigns = current_tenant.campaigns.includes(:email_campaign, :social_campaign)
                              .recent
    @pagy, @campaigns = pagy(@campaigns, items: 10) if respond_to?(:pagy)
  end

  def show
    # Campaign details with associated email/social campaign data
  end

  def new
    @campaign = current_tenant.campaigns.build
    @campaign.created_by = current_user
  end

  def create
    @campaign = current_tenant.campaigns.build(campaign_params)
    @campaign.created_by = current_user

    if @campaign.save
      redirect_to @campaign, notice: 'Campaign was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # Edit campaign form
  end

  def update
    if @campaign.update(campaign_params)
      redirect_to @campaign, notice: 'Campaign was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @campaign.destroy
    redirect_to campaigns_url, notice: 'Campaign was successfully deleted.'
  end

  def activate
    if @campaign.can_be_activated?
      @campaign.update(status: 'active')
      redirect_to @campaign, notice: 'Campaign activated successfully.'
    else
      redirect_to @campaign, alert: 'Campaign cannot be activated in its current state.'
    end
  end

  def pause
    if @campaign.active?
      @campaign.update(status: 'paused')
      redirect_to @campaign, notice: 'Campaign paused successfully.'
    else
      redirect_to @campaign, alert: 'Only active campaigns can be paused.'
    end
  end

  def complete
    if @campaign.active? || @campaign.paused?
      @campaign.update(status: 'completed')
      redirect_to @campaign, notice: 'Campaign completed successfully.'
    else
      redirect_to @campaign, alert: 'Campaign cannot be completed in its current state.'
    end
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:id])
  end

  def campaign_params
    params.require(:campaign).permit(
      :name, :description, :campaign_type, :target_audience,
      :start_date, :end_date, :budget_cents,
      settings: {}
    )
  end
end
