<!-- Campaign Show Page -->

<!-- <PERSON> Header -->
<div class="mb-8">
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
    <div class="flex-1">
      <div class="flex items-center space-x-4 mb-4">
        <h1 class="text-3xl font-bold text-gray-900"><%= @campaign.name %></h1>
        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
          <%= case @campaign.status
              when 'active' then 'bg-green-100 text-green-800'
              when 'draft' then 'bg-gray-100 text-gray-800'
              when 'paused' then 'bg-yellow-100 text-yellow-800'
              when 'completed' then 'bg-blue-100 text-blue-800'
              when 'cancelled' then 'bg-red-100 text-red-800'
              else 'bg-gray-100 text-gray-800'
              end %>">
          <%= @campaign.status.titleize %>
        </span>
      </div>

      <p class="text-gray-600 mb-4"><%= @campaign.description %></p>

      <div class="flex flex-wrap items-center gap-6 text-sm text-gray-500">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
          </svg>
          <%= @campaign.campaign_type.titleize %>
        </div>

        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
          $<%= number_with_precision(@campaign.budget_in_dollars, precision: 0, delimiter: ',') %> Budget
        </div>

        <% if @campaign.start_date %>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <%= @campaign.start_date.strftime("%b %d, %Y") %>
            <% if @campaign.end_date %>
              - <%= @campaign.end_date.strftime("%b %d, %Y") %>
            <% end %>
          </div>
        <% end %>

        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          <%= @campaign.target_audience %>
        </div>
      </div>
    </div>

    <div class="mt-6 lg:mt-0 flex items-center space-x-4">
      <%= link_to edit_campaign_path(@campaign),
          class: "inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        Edit Campaign
      <% end %>

      <%= link_to campaigns_path,
          class: "inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Campaigns
      <% end %>
    </div>
  </div>
</div>

<!-- Campaign Progress -->
<% if @campaign.start_date && @campaign.end_date %>
  <div class="mb-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-lg font-semibold text-gray-900">Campaign Progress</h2>
      <span class="text-2xl font-bold text-blue-600"><%= @campaign.progress_percentage %>%</span>
    </div>

    <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
      <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500"
           style="width: <%= @campaign.progress_percentage %>%"></div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
      <div>
        <span class="text-gray-500">Start Date:</span>
        <span class="font-medium text-gray-900 ml-2"><%= @campaign.start_date.strftime("%b %d, %Y") %></span>
      </div>
      <div>
        <span class="text-gray-500">End Date:</span>
        <span class="font-medium text-gray-900 ml-2"><%= @campaign.end_date.strftime("%b %d, %Y") %></span>
      </div>
      <div>
        <span class="text-gray-500">Duration:</span>
        <span class="font-medium text-gray-900 ml-2">
          <% if @campaign.duration_in_days %>
            <%= @campaign.duration_in_days %> days
          <% else %>
            N/A
          <% end %>
        </span>
      </div>
    </div>
  </div>
<% end %>

<!-- Campaign Content Sections -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">

  <!-- Main Content Area -->
  <div class="lg:col-span-2 space-y-8">

    <!-- Campaign Type Specific Content -->
    <% case @campaign.campaign_type %>
    <% when 'email' %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900 flex items-center">
            <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Email Campaign Details
          </h2>
          <% if @campaign.email_campaign %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
              Configured
            </span>
          <% else %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
              Setup Required
            </span>
          <% end %>
        </div>

        <% if @campaign.email_campaign %>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Subject Line</label>
              <p class="text-gray-900 bg-gray-50 rounded-lg p-3"><%= @campaign.email_campaign.subject_line %></p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">From</label>
              <p class="text-gray-900"><%= @campaign.email_campaign.from_name %> &lt;<%= @campaign.email_campaign.from_email %>&gt;</p>
            </div>

            <% if @campaign.email_campaign.preview_text.present? %>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Preview Text</label>
                <p class="text-gray-900 bg-gray-50 rounded-lg p-3"><%= @campaign.email_campaign.preview_text %></p>
              </div>
            <% end %>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Content Preview</label>
              <div class="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                <p class="text-gray-900 text-sm"><%= @campaign.email_campaign.preview_snippet(200) %></p>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div>
                <span class="text-sm text-gray-500">Recipients</span>
                <p class="text-lg font-semibold text-gray-900"><%= number_with_delimiter(@campaign.email_campaign.recipient_count) %></p>
              </div>
              <div>
                <span class="text-sm text-gray-500">Est. Send Time</span>
                <p class="text-lg font-semibold text-gray-900"><%= @campaign.email_campaign.estimated_send_time %> min</p>
              </div>
            </div>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Email Content Not Set Up</h3>
            <p class="text-gray-600 mb-4">Configure your email content to start sending campaigns.</p>
            <button class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Setup Email Content
            </button>
          </div>
        <% end %>
      </div>

    <% when 'social' %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900 flex items-center">
            <svg class="w-6 h-6 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
            </svg>
            Social Media Campaign
          </h2>
          <% if @campaign.social_campaign %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
              Configured
            </span>
          <% else %>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
              Setup Required
            </span>
          <% end %>
        </div>

        <% if @campaign.social_campaign %>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Platforms</label>
              <div class="flex flex-wrap gap-2">
                <% @campaign.social_campaign.platforms.each do |platform| %>
                  <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800">
                    <%= platform.titleize %>
                  </span>
                <% end %>
              </div>
            </div>

            <% if @campaign.social_campaign.hashtags.present? %>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Hashtags</label>
                <div class="flex flex-wrap gap-2">
                  <% @campaign.social_campaign.hashtag_list.each do |hashtag| %>
                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800">
                      <%= hashtag.start_with?('#') ? hashtag : "##{hashtag}" %>
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Content Variants</label>
              <div class="space-y-3">
                <% @campaign.social_campaign.content_variants.each do |platform, content| %>
                  <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-900"><%= platform.titleize %></span>
                      <span class="text-xs text-gray-500"><%= content.length %> characters</span>
                    </div>
                    <p class="text-sm text-gray-700"><%= truncate(content, length: 150) %></p>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
              <div>
                <span class="text-sm text-gray-500">Est. Reach</span>
                <p class="text-lg font-semibold text-gray-900"><%= number_with_delimiter(@campaign.social_campaign.estimated_reach) %></p>
              </div>
              <div>
                <span class="text-sm text-gray-500">Scheduled Posts</span>
                <p class="text-lg font-semibold text-gray-900"><%= @campaign.social_campaign.scheduled_posts.count %></p>
              </div>
            </div>
          </div>
        <% else %>
          <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Social Content Not Set Up</h3>
            <p class="text-gray-600 mb-4">Configure your social media content and platforms.</p>
            <button class="inline-flex items-center px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Setup Social Content
            </button>
          </div>
        <% end %>
      </div>
