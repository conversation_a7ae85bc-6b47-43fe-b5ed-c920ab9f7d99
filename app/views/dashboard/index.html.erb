<!-- Dashboard Header -->
<div class="mb-8">
  <h1 class="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
  <p class="text-gray-600">Welcome back! Here's what's happening with your campaigns.</p>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <!-- Total Campaigns -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600">Total Campaigns</p>
        <p class="text-2xl font-bold text-gray-900"><%= @campaign_stats[:total] %></p>
      </div>
      <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Active Campaigns -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600">Active Campaigns</p>
        <p class="text-2xl font-bold text-green-600"><%= @campaign_stats[:active] %></p>
      </div>
      <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Total Budget -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600">Total Budget</p>
        <p class="text-2xl font-bold text-gray-900">$<%= number_with_precision(@budget_stats[:total_budget], precision: 0, delimiter: ',') %></p>
      </div>
      <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
        </svg>
      </div>
    </div>
  </div>

  <!-- Budget Spent -->
  <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600">Budget Spent</p>
        <p class="text-2xl font-bold text-orange-600">$<%= number_with_precision(@budget_stats[:spent_budget], precision: 0, delimiter: ',') %></p>
      </div>
      <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
      </div>
    </div>
  </div>
</div>

<!-- Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
  <!-- Recent Campaigns -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-100">
    <div class="p-6 border-b border-gray-100">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-semibold text-gray-900">Recent Campaigns</h2>
        <%= link_to campaigns_path, class: "text-blue-600 hover:text-blue-700 text-sm font-medium" do %>
          View All →
        <% end %>
      </div>
    </div>
    <div class="p-6">
      <% if @recent_campaigns.any? %>
        <div class="space-y-4">
          <% @recent_campaigns.each do |campaign| %>
            <div class="flex items-center justify-between p-4 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center
                  <%= case campaign.status
                      when 'active' then 'bg-green-100 text-green-600'
                      when 'draft' then 'bg-gray-100 text-gray-600'
                      when 'completed' then 'bg-blue-100 text-blue-600'
                      else 'bg-gray-100 text-gray-600'
                      end %>">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                  </svg>
                </div>
                <div>
                  <h3 class="font-medium text-gray-900"><%= campaign.name %></h3>
                  <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <span class="px-2 py-1 rounded-full text-xs font-medium
                      <%= case campaign.status
                          when 'active' then 'bg-green-100 text-green-700'
                          when 'draft' then 'bg-gray-100 text-gray-700'
                          when 'completed' then 'bg-blue-100 text-blue-700'
                          else 'bg-gray-100 text-gray-700'
                          end %>">
                      <%= campaign.status.titleize %>
                    </span>
                    <span>•</span>
                    <span>$<%= number_with_precision(campaign.budget, precision: 0, delimiter: ',') %></span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <% if campaign.email_campaign %>
                  <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">@</span>
                <% end %>
                <% if campaign.social_campaign %>
                  <span class="w-6 h-6 bg-pink-100 text-pink-600 rounded-full flex items-center justify-center text-xs font-bold">#</span>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No campaigns yet</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by creating your first campaign.</p>
          <div class="mt-6">
            <%= link_to new_campaign_path, class: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors" do %>
              Create Campaign
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Platform Distribution -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-100">
    <div class="p-6 border-b border-gray-100">
      <h2 class="text-lg font-semibold text-gray-900">Platform Distribution</h2>
    </div>
    <div class="p-6">
      <div class="space-y-4">
        <!-- Email Campaigns -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <span class="text-blue-600 font-bold text-sm">@</span>
            </div>
            <span class="font-medium text-gray-900">Email Campaigns</span>
          </div>
          <span class="text-2xl font-bold text-gray-900"><%= @platform_stats[:email_campaigns] %></span>
        </div>
        
        <!-- Social Campaigns -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center">
              <span class="text-pink-600 font-bold text-sm">#</span>
            </div>
            <span class="font-medium text-gray-900">Social Campaigns</span>
          </div>
          <span class="text-2xl font-bold text-gray-900"><%= @platform_stats[:social_campaigns] %></span>
        </div>
        
        <!-- Multi-Channel -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <span class="text-purple-600 font-bold text-sm">⚡</span>
            </div>
            <span class="font-medium text-gray-900">Multi-Channel</span>
          </div>
          <span class="text-2xl font-bold text-gray-900"><%= @platform_stats[:multi_channel] %></span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Quick Actions -->
<div class="mt-8">
  <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">Ready to launch your next campaign?</h3>
        <p class="text-blue-100 mt-1">Create multi-channel campaigns with AI-powered insights.</p>
      </div>
      <%= link_to new_campaign_path, class: "bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors" do %>
        Create Campaign
      <% end %>
    </div>
  </div>
</div>
