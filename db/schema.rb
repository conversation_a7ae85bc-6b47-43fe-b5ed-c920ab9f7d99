# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_04_184910) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "campaigns", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.integer "campaign_type", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.string "target_audience", null: false
    t.date "start_date"
    t.date "end_date"
    t.integer "budget_cents", default: 0
    t.jsonb "settings", default: {}, null: false
    t.bigint "tenant_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_type"], name: "index_campaigns_on_campaign_type"
    t.index ["created_by_id"], name: "index_campaigns_on_created_by_id"
    t.index ["name", "tenant_id"], name: "index_campaigns_on_name_and_tenant_id", unique: true
    t.index ["settings"], name: "index_campaigns_on_settings", using: :gin
    t.index ["start_date"], name: "index_campaigns_on_start_date"
    t.index ["status"], name: "index_campaigns_on_status"
    t.index ["tenant_id"], name: "index_campaigns_on_tenant_id"
  end

  create_table "email_campaigns", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.string "subject_line", limit: 150, null: false
    t.string "preview_text", limit: 200
    t.text "content", null: false
    t.string "from_name", null: false
    t.string "from_email", null: false
    t.jsonb "settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_email_campaigns_on_campaign_id", unique: true
    t.index ["from_email"], name: "index_email_campaigns_on_from_email"
    t.index ["settings"], name: "index_email_campaigns_on_settings", using: :gin
  end

  create_table "social_campaigns", force: :cascade do |t|
    t.bigint "campaign_id", null: false
    t.jsonb "platforms", default: [], null: false
    t.jsonb "content_variants", default: {}, null: false
    t.string "hashtags", default: ""
    t.jsonb "target_demographics", default: {}, null: false
    t.jsonb "post_schedule", default: {}, null: false
    t.jsonb "social_settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["campaign_id"], name: "index_social_campaigns_on_campaign_id", unique: true
    t.index ["content_variants"], name: "index_social_campaigns_on_content_variants", using: :gin
    t.index ["platforms"], name: "index_social_campaigns_on_platforms", using: :gin
    t.index ["post_schedule"], name: "index_social_campaigns_on_post_schedule", using: :gin
    t.index ["social_settings"], name: "index_social_campaigns_on_social_settings", using: :gin
    t.index ["target_demographics"], name: "index_social_campaigns_on_target_demographics", using: :gin
  end

  create_table "tenants", force: :cascade do |t|
    t.string "name", null: false
    t.string "subdomain", null: false
    t.string "status", default: "active", null: false
    t.jsonb "settings", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_tenants_on_name", unique: true
    t.index ["settings"], name: "index_tenants_on_settings", using: :gin
    t.index ["status"], name: "index_tenants_on_status"
    t.index ["subdomain"], name: "index_tenants_on_subdomain", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "confirmation_token"
    t.datetime "confirmation_sent_at"
    t.datetime "confirmed_at"
    t.string "unconfirmed_email"
    t.bigint "tenant_id", null: false
    t.string "first_name", null: false
    t.string "last_name", null: false
    t.integer "role", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email", "tenant_id"], name: "index_users_on_email_and_tenant_id", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["role"], name: "index_users_on_role"
    t.index ["tenant_id"], name: "index_users_on_tenant_id"
  end

  add_foreign_key "campaigns", "tenants"
  add_foreign_key "campaigns", "users", column: "created_by_id"
  add_foreign_key "email_campaigns", "campaigns"
  add_foreign_key "social_campaigns", "campaigns"
  add_foreign_key "users", "tenants"
end
